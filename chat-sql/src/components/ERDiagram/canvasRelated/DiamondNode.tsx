import React from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import { Tooltip, useTheme } from '@mui/material';
import { useERDiagramContext } from '@/contexts/ERDiagramContext';
import InlineEditor from '../utils/InlineEditor';
import styles from './DiamondNode.module.css';

// 菱形节点的数据类型
export interface DiamondNodeData {
  label: string;
  description?: string;
  attributes?: Array<{
    id: string;
    name: string;
    dataType?: string;
  }>;
  isWeakRelationship?: boolean; // 是否为弱关系
  [key: string]: unknown; // 添加索引签名
}

// 关系描述内容组件
const RelationTooltipContent: React.FC<{ description?: string; attributes?: DiamondNodeData['attributes'] }> = ({ description, attributes }) => (
  <div style={{ minWidth: 120, maxWidth: 260, fontSize: 13, color: 'inherit', lineHeight: 1.6 }}>
    {description && <div style={{ marginBottom: 4 }}>{description}</div>}
    {attributes && attributes.length > 0 && (
      <ul style={{ paddingLeft: 16, margin: 0 }}>
        {attributes.map(attr => (
          <li key={attr.id} style={{ marginBottom: 2 }}>
            {attr.name}{attr.dataType ? ` : ${attr.dataType}` : ''}
          </li>
        ))}
      </ul>
    )}
  </div>
);

// 菱形节点组件
const DiamondNode: React.FC<NodeProps> = ({ data, selected, id }) => {
  const { label, description, attributes, isWeakRelationship } = data as DiamondNodeData;
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';

  // 使用ERDiagram上下文
  const { state, startEditNode, finishEditNode, renameNode, selectNode, setActiveTab, setSelectedElement } = useERDiagramContext();

  // 判断当前节点是否处于编辑状态
  const isEditing = state.editingNodeId === id && state.nodeEditMode === 'rename';

  // 双击标题重命名
  const handleTitleDoubleClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (!isEditing) {
      startEditNode(id, 'rename');
    }
  };

  // 双击关系内部 - 切换到关系列表并选中
  const handleRelationshipDoubleClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    setActiveTab('relationships');
    setSelectedElement(id);
  };

  // 保存重命名
  const handleSaveRename = (newName: string) => {
    renameNode(id, newName);
  };

  // 取消重命名
  const handleCancelRename = () => {
    finishEditNode();
  };

  // 单击选中节点
  const handleClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    selectNode(id);
  };

  // 右键菜单 - 进入属性编辑模式
  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    selectNode(id);
    startEditNode(id, 'properties');
  };
  
  // 菱形的尺寸
  const width = 160;
  const height = 100;
  const centerX = width / 2;
  const centerY = height / 2;
  
  // 菱形的四个顶点坐标
  const points = [
    `${centerX},10`,           // 上顶点
    `${width - 10},${centerY}`, // 右顶点
    `${centerX},${height - 10}`, // 下顶点
    `10,${centerY}`            // 左顶点
  ].join(' ');

  return (
    <Tooltip
      title={<RelationTooltipContent description={description} attributes={attributes} />}
      placement="right"
      arrow
      componentsProps={{
        tooltip: {
          sx: {
            bgcolor: isDark ? '#444' : '#fff',
            color: isDark ? '#fff' : '#222',
            boxShadow: 3,
            borderRadius: 2,
            fontSize: 13,
            px: 2,
            py: 1.5,
          }
        },
        arrow: {
          sx: {
            color: isDark ? '#444' : '#fff',
          }
        }
      }}
    >
      <div
        className={`${styles.diamondNode} ${selected ? styles.selected : ''} ${isWeakRelationship ? styles.weakRelationship : ''}`}
        onClick={handleClick}
        onContextMenu={handleContextMenu}
        onDoubleClick={handleRelationshipDoubleClick}
      >
        {/* 上方连接点 */}
        <Handle type="source" position={Position.Top} id="top" className={styles.handle} style={{ top: '10px', left: '50%', transform: 'translateX(-50%)' }} />
        <Handle type="target" position={Position.Top} id="top" className={styles.handle} style={{ top: '10px', left: '50%', transform: 'translateX(-50%)' }} />
        
        {/* 右侧连接点 */}
        <Handle type="source" position={Position.Right} id="right" className={styles.handle} style={{ top: '50%', right: '10px', transform: 'translateY(-50%)' }} />
        <Handle type="target" position={Position.Right} id="right" className={styles.handle} style={{ top: '50%', right: '10px', transform: 'translateY(-50%)' }} />

        {/* 下方连接点 */}
        <Handle type="source" position={Position.Bottom} id="bottom" className={styles.handle} style={{ bottom: '10px', left: '50%', transform: 'translateX(-50%)' }} />
        <Handle type="target" position={Position.Bottom} id="bottom" className={styles.handle} style={{ bottom: '10px', left: '50%', transform: 'translateX(-50%)' }} />

        {/* 左侧连接点 */}
        <Handle type="source" position={Position.Left} id="left" className={styles.handle} style={{ top: '50%', left: '10px', transform: 'translateY(-50%)' }} />
        <Handle type="target" position={Position.Left} id="left" className={styles.handle} style={{ top: '50%', left: '10px', transform: 'translateY(-50%)' }} />
        
        {/* SVG 菱形 */}
        <svg
          width={width}
          height={height}
          className={styles.diamondSvg}
          viewBox={`0 0 ${width} ${height}`}
        >
          <polygon
            points={points}
            className={styles.diamondShape}
            fill="#e1f5fe"
            stroke="#0277bd"
            strokeWidth={isWeakRelationship ? "4" : "2"}
          />
          {/* 弱关系的内部边框 */}
          {isWeakRelationship && (
            <polygon
              points={[
                `${centerX},15`,           // 上顶点（内缩）
                `${width - 18},${centerY}`, // 右顶点（内缩）
                `${centerX},${height - 15}`, // 下顶点（内缩）
                `18,${centerY}`            // 左顶点（内缩）
              ].join(' ')}
              className={styles.innerDiamondShape}
            />
          )}
        </svg>
        {/* 关系名称 - 支持双击编辑 */}
        <div className={styles.labelContainer} onDoubleClick={handleTitleDoubleClick}>
          {isEditing ? (
            <InlineEditor
              nodeId={id}
              currentName={label}
              onSave={handleSaveRename}
              onCancel={handleCancelRename}
              className={styles.inlineEditor}
            />
          ) : (
            <div className={styles.label}>{label}</div>
          )}
        </div>
      </div>
    </Tooltip>
  );
};

export default DiamondNode;
